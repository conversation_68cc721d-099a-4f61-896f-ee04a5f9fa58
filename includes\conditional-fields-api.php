<?php
/**
 * Helper function to scan form content for fields
 */
function la_addons_scan_form_for_fields($form_id) {
    $form_post = get_post($form_id);
    if (!$form_post) {
        return array();
    }
    
    // Get form content
    $form_content = get_post_meta($form_id, '_form', true);
    if (empty($form_content)) {
        return array();
    }
    
    // Parse form content to find fields
    $fields = array();
    
    // Match all form tags
    preg_match_all('/\[([a-zA-Z0-9_-]+)\s+([^\]]*)\]/', $form_content, $matches, PREG_SET_ORDER);
    
    foreach ($matches as $match) {
        $tag_type = $match[1];
        $tag_attrs = $match[2];
        
        // Skip submit buttons
        if ($tag_type === 'submit') {
            continue;
        }
        
        // Extract name attribute
        if (preg_match('/name="([^"]+)"/', $tag_attrs, $name_match)) {
            $field_name = $name_match[1];
            
            // Extract options for select, checkbox, radio
            $options = array();
            if (in_array($tag_type, array('select', 'checkbox', 'radio'))) {
                // Look for "option1|option2" format
                if (preg_match('/"([^"]*)"/', $tag_attrs, $option_match)) {
                    $option_string = $option_match[1];
                    $options = explode('|', $option_string);
                }
            }
            
            $fields[$field_name] = array(
                'name' => $field_name,
                'type' => $tag_type,
                'basetype' => $tag_type,
                'options' => $options
            );
        }
    }
    
    return $fields;
}

/**
 * API Endpoints for Conditional Fields
 */
function la_addons_register_conditional_logic_api() {
    register_rest_field(
        'contact-form-7',
        'conditionalLogic',
        array(
            'get_callback' => 'la_addons_get_conditional_logic_for_api',
            'schema' => null,
        )
    );
}
add_action('rest_api_init', 'la_addons_register_conditional_logic_api');

function la_addons_get_conditional_logic_for_api($object) {
    $form_id = $object['id'];
    $rules = get_post_meta($form_id, '_la_addons_conditional_logic', true);
    
    if (empty($rules)) {
        return array();
    }
    
    return $rules;
}

