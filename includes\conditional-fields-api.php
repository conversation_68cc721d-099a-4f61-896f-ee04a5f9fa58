<?php
/**
 * Helper function to scan form content for fields
 */
function la_addons_scan_form_for_fields($form_id) {
    $form_post = get_post($form_id);
    if (!$form_post || $form_post->post_type !== 'wpcf7_contact_form') {
        return array();
    }

    // Get form content - try multiple methods
    $form_content = get_post_meta($form_id, '_form', true);

    // If _form meta doesn't exist, try getting it from the CF7 object
    if (empty($form_content)) {
        $contact_form = wpcf7_contact_form($form_id);
        if ($contact_form) {
            $form_content = $contact_form->prop('form');
        }
    }

    if (empty($form_content)) {
        error_log("No form content found for form ID: $form_id");
        return array();
    }
    
    // Parse form content to find fields
    $fields = array();

    error_log("Scanning form content for fields. Content length: " . strlen($form_content));

    // Match all form tags - improved regex to handle more cases
    preg_match_all('/\[([a-zA-Z0-9_*-]+)(?:\*)?(?:\s+([^\]]*))?\]/', $form_content, $matches, PREG_SET_ORDER);

    error_log("Found " . count($matches) . " form tags");

    foreach ($matches as $match) {
        $tag_type = $match[1];
        $tag_attrs = isset($match[2]) ? $match[2] : '';

        // Skip submit buttons and other non-input tags
        if (in_array($tag_type, array('submit', 'acceptance'))) {
            continue;
        }

        // Extract name attribute
        if (preg_match('/(?:^|\s)([a-zA-Z0-9_-]+)(?:\s|$)/', $tag_attrs, $name_match)) {
            $field_name = $name_match[1];

            // Skip if name looks like an attribute value
            if (strpos($field_name, '=') !== false || strpos($field_name, '"') !== false) {
                // Try to extract from name="fieldname" format
                if (preg_match('/name="([^"]+)"/', $tag_attrs, $explicit_name_match)) {
                    $field_name = $explicit_name_match[1];
                } else {
                    continue;
                }
            }

            // Extract options for select, checkbox, radio
            $options = array();
            if (in_array($tag_type, array('select', 'checkbox', 'radio'))) {
                // Look for quoted options
                if (preg_match_all('/"([^"]*)"/', $tag_attrs, $option_matches)) {
                    foreach ($option_matches[1] as $option_string) {
                        if (strpos($option_string, '|') !== false) {
                            $options = array_merge($options, explode('|', $option_string));
                        } else if (!empty($option_string) && $option_string !== $field_name) {
                            $options[] = $option_string;
                        }
                    }
                }
            }

            $fields[$field_name] = array(
                'name' => $field_name,
                'type' => $tag_type,
                'basetype' => $tag_type,
                'options' => $options
            );

            error_log("Found field: $field_name (type: $tag_type)");
        }
    }

    error_log("Total fields detected: " . count($fields));
    return $fields;
}

/**
 * API Endpoints for Conditional Fields
 */
function la_addons_register_conditional_logic_api() {
    // Register REST field for backward compatibility
    register_rest_field(
        'contact-form-7',
        'conditionalLogic',
        array(
            'get_callback' => 'la_addons_get_conditional_logic_for_api',
            'schema' => null,
        )
    );

    // Register custom REST endpoint for conditional logic
    register_rest_route('la-addons/v1', '/forms/(?P<id>\d+)/conditional-logic', array(
        'methods' => 'GET',
        'callback' => 'la_addons_get_conditional_logic_endpoint',
        'permission_callback' => '__return_true', // Public endpoint for frontend use
        'args' => array(
            'id' => array(
                'validate_callback' => function($param, $request, $key) {
                    return is_numeric($param);
                }
            ),
        ),
    ));
}
add_action('rest_api_init', 'la_addons_register_conditional_logic_api');

function la_addons_get_conditional_logic_for_api($object) {
    $form_id = $object['id'];
    $rules = get_post_meta($form_id, '_la_addons_conditional_logic', true);

    if (empty($rules)) {
        return array();
    }

    return $rules;
}

function la_addons_get_conditional_logic_endpoint($request) {
    $form_id = $request['id'];

    // Verify the form exists
    $form = get_post($form_id);
    if (!$form || $form->post_type !== 'wpcf7_contact_form') {
        return new WP_Error('form_not_found', 'Contact form not found', array('status' => 404));
    }

    $rules = get_post_meta($form_id, '_la_addons_conditional_logic', true);

    if (empty($rules)) {
        $rules = array();
    }

    return rest_ensure_response(array(
        'success' => true,
        'data' => array(
            'conditionalLogic' => $rules
        )
    ));
}

