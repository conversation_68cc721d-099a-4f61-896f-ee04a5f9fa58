<?php

function la_addons_save_submission( $contact_form ) {
    $submission = WPCF7_Submission::get_instance();

    if ( $submission ) {
        $form_data = $submission->get_posted_data();
        $form_id = $contact_form->id();
        $uploaded_files = $submission->uploaded_files();

        foreach ( $form_data as $field_name => $field_value ) {
            if ( strpos( $field_value, 'data:image/png;base64,' ) === 0 ) {
                $img = str_replace('data:image/png;base64,', '', $field_value);
                $img = str_replace(' ', '+', $img);
                $data = base64_decode($img);
                $file_name = 'signature-' . time() . '.png';
                $upload_dir = wp_upload_dir();
                $new_file_path = $upload_dir['path'] . '/' . $file_name;
                file_put_contents($new_file_path, $data);

                $attachment = array(
                    'guid'           => $new_file_path,
                    'post_mime_type' => mime_content_type( $new_file_path ),
                    'post_title'     => preg_replace( '/\.[^.]+$/', '', $file_name ),
                    'post_content'   => '',
                    'post_status'    => 'inherit'
                );

                $attach_id = wp_insert_attachment( $attachment, $new_file_path );
                require_once( ABSPATH . 'wp-admin/includes/image.php' );
                $attach_data = wp_generate_attachment_metadata( $attach_id, $new_file_path );
                wp_update_attachment_metadata( $attach_id, $attach_data );

                $form_data[$field_name] = wp_get_attachment_url( $attach_id );
            }
        }

        foreach ( $uploaded_files as $field_name => $file_path ) {
            $file_name = basename( $file_path );
            $upload_dir = wp_upload_dir();
            $new_file_path = $upload_dir['path'] . '/' . $file_name;
            copy( $file_path, $new_file_path );

            $attachment = array(
                'guid'           => $new_file_path,
                'post_mime_type' => mime_content_type( $new_file_path ),
                'post_title'     => preg_replace( '/\.[^.]+$/', '', $file_name ),
                'post_content'   => '',
                'post_status'    => 'inherit'
            );

            $attach_id = wp_insert_attachment( $attachment, $new_file_path );
            require_once( ABSPATH . 'wp-admin/includes/image.php' );
            $attach_data = wp_generate_attachment_metadata( $attach_id, $new_file_path );
            wp_update_attachment_metadata( $attach_id, $attach_data );

            $form_data[$field_name] = wp_get_attachment_url( $attach_id );
        }

        global $wpdb;
        $table_name = $wpdb->prefix . 'la_addons_submissions';

        $wpdb->insert(
            $table_name,
            array(
                'form_id' => $form_id,
                'submission_time' => current_time( 'mysql' ),
                'form_data' => serialize( $form_data ),
            )
        );
    }
}

add_action( 'wpcf7_mail_sent', 'la_addons_save_submission' );
