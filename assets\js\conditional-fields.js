/**
 * Conditional Fields for Contact Form 7
 * Enhanced version with multiple conditions and advanced operators
 */
document.addEventListener("DOMContentLoaded", function () {
  const conditionalLogicBuilder = document.getElementById(
    "la-addons-conditional-logic-builder"
  );

  if (!conditionalLogicBuilder) return;

  const addRuleGroupButton = document.getElementById(
    "la-addons-add-rule-group"
  );
  const rulesContainer =
    conditionalLogicBuilder.querySelector(".rules-container");
  const ruleGroupTemplate = document.querySelector(
    "#rule-templates .rule-group-template"
  );
  const conditionTemplate = document.querySelector(
    "#rule-templates .condition-template"
  );

  // Check if templates exist
  if (!ruleGroupTemplate || !conditionTemplate) {
    console.error("Required templates not found for conditional logic builder");
    return;
  }

  const ruleGroupTemplateHtml = ruleGroupTemplate.innerHTML;
  const conditionTemplateHtml = conditionTemplate.innerHTML;

  // Get form fields from the global variable
  const formFields =
    typeof laAddonsConditionalFields !== "undefined"
      ? laAddonsConditionalFields.fields || []
      : [];

  console.log("Available form fields:", formFields);

  // Check if we have fields
  if (formFields.length === 0) {
    console.error("No form fields found for conditional logic");
    // Add a warning message to the UI
    const warningMessage = document.createElement("div");
    warningMessage.className = "notice notice-warning";
    warningMessage.innerHTML =
      "<p>No form fields detected. Please add some fields to your form before setting up conditional logic.</p>";
    conditionalLogicBuilder.prepend(warningMessage);
  }

  // Add new rule group
  addRuleGroupButton.addEventListener("click", function () {
    // Only count rule groups that are NOT in the template container
    const groupIndex = rulesContainer.querySelectorAll(".rule-group").length;
    const groupNumber = groupIndex + 1;

    // Remove "no rules" message if it exists
    const noRulesMessage = rulesContainer.querySelector(".no-rules-message");
    if (noRulesMessage) {
      noRulesMessage.remove();
    }

    // Create new rule group from template
    rulesContainer.insertAdjacentHTML(
      "beforeend",
      ruleGroupTemplateHtml
        .replace(/\{\{GROUP_INDEX\}\}/g, groupIndex)
        .replace(/\{\{GROUP_NUMBER\}\}/g, groupNumber)
    );

    // Add first condition to the new group
    const newGroup = rulesContainer.querySelector(
      `.rule-group[data-group-index="${groupIndex}"]`
    );

    // Enable all form elements in the new group (they were disabled in template)
    newGroup.querySelectorAll("select, input").forEach((element) => {
      element.disabled = false;
    });

    addCondition(newGroup, 0);

    // Initialize event listeners for the new group
    initRuleGroupEvents(newGroup);
  });

  // Initialize existing rule groups (only those in the rules container, not templates)
  rulesContainer.querySelectorAll(".rule-group").forEach((group) => {
    initRuleGroupEvents(group);
  });

  // Initialize event listeners for a rule group
  function initRuleGroupEvents(group) {
    // Delete rule group
    group
      .querySelector(".delete-rule-group")
      .addEventListener("click", function () {
        if (confirm("Are you sure you want to delete this rule group?")) {
          group.remove();

          // If no rules left, show the "no rules" message
          if (rulesContainer.querySelectorAll(".rule-group").length === 0) {
            rulesContainer.innerHTML = `
            <div class="no-rules-message">
              <p>${
                laAddonsConditionalFields?.i18n?.noRules ||
                "No conditional rules have been created yet."
              }</p>
            </div>
          `;
          }

          // Update group numbers
          updateGroupNumbers();
        }
      });

    // Add condition
    group
      .querySelector(".add-condition")
      .addEventListener("click", function () {
        const groupIndex = group.dataset.groupIndex;
        const conditionIndex = group.querySelectorAll(".condition-row").length;
        addCondition(group, conditionIndex);
      });

    // Initialize existing conditions
    group.querySelectorAll(".condition-row").forEach((conditionRow) => {
      initConditionEvents(conditionRow);
    });
  }

  // Add a new condition to a rule group
  function addCondition(group, conditionIndex) {
    const groupIndex = group.dataset.groupIndex;
    const conditionsContainer = group.querySelector(".conditions-container");

    conditionsContainer.insertAdjacentHTML(
      "beforeend",
      conditionTemplateHtml
        .replace(/\{\{GROUP_INDEX\}\}/g, groupIndex)
        .replace(/\{\{CONDITION_INDEX\}\}/g, conditionIndex)
    );

    // Initialize event listeners for the new condition
    const newCondition = conditionsContainer.querySelector(
      `.condition-row[data-condition-index="${conditionIndex}"]`
    );

    // Enable all form elements in the new condition (they were disabled in template)
    newCondition.querySelectorAll("select, input").forEach((element) => {
      element.disabled = false;
    });

    initConditionEvents(newCondition);
  }

  // Initialize event listeners for a condition
  function initConditionEvents(conditionRow) {
    // Delete condition
    conditionRow
      .querySelector(".delete-condition")
      .addEventListener("click", function () {
        const conditionsContainer = conditionRow.closest(
          ".conditions-container"
        );

        // Don't allow deleting the last condition
        if (conditionsContainer.querySelectorAll(".condition-row").length > 1) {
          conditionRow.remove();
          updateConditionIndexes(conditionsContainer);
        } else {
          alert("You must have at least one condition in a rule group.");
        }
      });

    // Toggle value input based on operator
    const operatorSelect = conditionRow.querySelector(".operator-select");
    const valueInput = conditionRow.querySelector(".condition-value-input");

    if (operatorSelect && valueInput) {
      operatorSelect.addEventListener("change", function () {
        const operator = this.value;
        if (operator === "is_empty" || operator === "is_not_empty") {
          valueInput.style.display = "none";
          valueInput.value = "";
        } else {
          valueInput.style.display = "";
        }
      });
    }

    // Show field-specific options for the value input
    const fieldSelect = conditionRow.querySelector(".condition-field-select");
    if (fieldSelect && valueInput) {
      fieldSelect.addEventListener("change", function () {
        const selectedField = this.value;
        const selectedFieldData = formFields.find(
          (field) => field.name === selectedField
        );

        if (selectedFieldData) {
          // For radio buttons, checkboxes, and select fields, show available options
          if (
            ["radio", "checkbox", "select"].includes(selectedFieldData.basetype)
          ) {
            // Create datalist for suggestions if it doesn't exist
            let datalistId = `field-options-${selectedField.replace(
              /[^a-zA-Z0-9]/g,
              "-"
            )}`;
            let datalist = document.getElementById(datalistId);

            if (!datalist) {
              datalist = document.createElement("datalist");
              datalist.id = datalistId;
              document.body.appendChild(datalist);

              // Add options to datalist
              if (
                selectedFieldData.options &&
                selectedFieldData.options.length
              ) {
                selectedFieldData.options.forEach((option) => {
                  const optionEl = document.createElement("option");
                  optionEl.value = option;
                  datalist.appendChild(optionEl);
                });
              }
            }

            // Connect datalist to input
            valueInput.setAttribute("list", datalistId);
          } else {
            // Remove datalist for other field types
            valueInput.removeAttribute("list");
          }
        }
      });
    }
  }

  // Update group numbers after deletion
  function updateGroupNumbers() {
    // Only update rule groups that are NOT in the template container
    rulesContainer.querySelectorAll(".rule-group").forEach((group, index) => {
      group.dataset.groupIndex = index;
      const heading = group.querySelector("h3");
      if (heading) {
        heading.textContent = `${
          laAddonsConditionalFields?.i18n?.ruleGroup || "Rule Group"
        } #${index + 1}`;
      }

      // Update all name attributes in this group
      group
        .querySelectorAll("[name^='la_addons_conditional_logic[']")
        .forEach((element) => {
          element.name = element.name.replace(
            /la_addons_conditional_logic\[\d+\]/,
            `la_addons_conditional_logic[${index}]`
          );
        });
    });
  }

  // Update condition indexes after deletion
  function updateConditionIndexes(conditionsContainer) {
    conditionsContainer
      .querySelectorAll(".condition-row")
      .forEach((condition, index) => {
        condition.dataset.conditionIndex = index;

        // Update all name attributes in this condition
        condition
          .querySelectorAll("[name*='[conditions][']")
          .forEach((element) => {
            element.name = element.name.replace(
              /\[conditions\]\[\d+\]/,
              `[conditions][${index}]`
            );
          });
      });
  }
});

/**
 * Frontend conditional logic implementation
 */
function initializeConditionalLogic() {
  // Only run on the frontend
  if (document.body.classList.contains("wp-admin")) return;

  const forms = document.querySelectorAll(".wpcf7-form");
  console.log("Found " + forms.length + " CF7 forms to initialize");

  forms.forEach((form) => {
    // Skip if already initialized
    if (form.dataset.conditionalLogicInitialized === "true") {
      return;
    }

    // Mark as initialized
    form.dataset.conditionalLogicInitialized = "true";
    const formId = form.querySelector('input[name="_wpcf7"]')?.value;

    if (!formId) return;

    // Try AJAX method first (more reliable with WordPress permissions)
    tryAjaxConditionalLogic(form, formId);

    // Primary method: AJAX request
    function tryAjaxConditionalLogic(form, formId) {
      // Use localized variables if available, fallback to CF7 API
      const ajaxUrl =
        typeof laAddonsConditionalLogic !== "undefined" &&
        laAddonsConditionalLogic.ajaxUrl
          ? laAddonsConditionalLogic.ajaxUrl
          : wpcf7.api.root.replace("/wp-json/", "/wp-admin/admin-ajax.php");

      const nonce =
        typeof laAddonsConditionalLogic !== "undefined" &&
        laAddonsConditionalLogic.nonce
          ? laAddonsConditionalLogic.nonce
          : wpcf7.api.nonce || "fallback-nonce";

      const formData = new FormData();
      formData.append("action", "la_addons_get_conditional_logic");
      formData.append("form_id", formId);
      formData.append("nonce", nonce);

      fetch(ajaxUrl, {
        method: "POST",
        body: formData,
      })
        .then((response) => response.json())
        .then((data) => {
          console.log("AJAX conditional logic response:", data);
          if (data.success && data.data && data.data.conditionalLogic) {
            initConditionalLogic(form, data.data.conditionalLogic);
          } else {
            console.log(
              "No conditional logic rules found via AJAX for form " + formId
            );
            // Try REST API fallback
            tryRestApiConditionalLogic(form, formId);
          }
        })
        .catch((error) => {
          console.error("AJAX method failed:", error);
          // Try REST API fallback
          tryRestApiConditionalLogic(form, formId);
        });
    }

    // Fallback method 1: REST API endpoint
    function tryRestApiConditionalLogic(form, formId) {
      const restUrl =
        typeof laAddonsConditionalLogic !== "undefined" &&
        laAddonsConditionalLogic.restUrl
          ? laAddonsConditionalLogic.restUrl
          : wpcf7.api.root;

      const restNonce =
        typeof laAddonsConditionalLogic !== "undefined" &&
        laAddonsConditionalLogic.restNonce
          ? laAddonsConditionalLogic.restNonce
          : wpcf7.api.nonce;

      fetch(restUrl + "la-addons/v1/forms/" + formId + "/conditional-logic", {
        method: "GET",
        headers: {
          "X-WP-Nonce": restNonce,
        },
      })
        .then((response) => {
          if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
          }
          return response.json();
        })
        .then((data) => {
          console.log("REST API conditional logic response:", data);
          if (data.success && data.data && data.data.conditionalLogic) {
            initConditionalLogic(form, data.data.conditionalLogic);
          } else {
            console.log(
              "No conditional logic rules found via REST API for form " + formId
            );
            // Try final fallback
            tryFallbackConditionalLogic(form, formId);
          }
        })
        .catch((error) => {
          console.error("REST API method failed:", error);
          // Try final fallback
          tryFallbackConditionalLogic(form, formId);
        });
    }

    // Fallback method 2: REST field approach
    function tryFallbackConditionalLogic(form, formId) {
      const restUrl =
        typeof laAddonsConditionalLogic !== "undefined" &&
        laAddonsConditionalLogic.restUrl
          ? laAddonsConditionalLogic.restUrl
          : wpcf7.api.root;

      const restNonce =
        typeof laAddonsConditionalLogic !== "undefined" &&
        laAddonsConditionalLogic.restNonce
          ? laAddonsConditionalLogic.restNonce
          : wpcf7.api.nonce;

      fetch(restUrl + "contact-form-7/v1/forms/" + formId, {
        method: "GET",
        headers: {
          "X-WP-Nonce": restNonce,
        },
      })
        .then((response) => response.json())
        .then((data) => {
          console.log("Fallback response:", data);
          if (data.conditionalLogic && data.conditionalLogic.length > 0) {
            initConditionalLogic(form, data.conditionalLogic);
          } else {
            console.log("No conditional logic found via any method");
          }
        })
        .catch((error) => {
          console.error("All methods failed:", error);
        });
    }

    // Initialize conditional logic with the rules
    function initConditionalLogic(form, rules) {
      // Get all form inputs
      const inputs = form.querySelectorAll("input, select, textarea");

      // Add event listeners to all inputs
      inputs.forEach((input) => {
        const events = ["change", "input", "keyup"];
        events.forEach((event) => {
          input.addEventListener(event, () => evaluateRules(form, rules));
        });
      });

      // Initial evaluation
      evaluateRules(form, rules);
    }

    // Evaluate all rules
    function evaluateRules(form, rules) {
      console.log("Evaluating " + rules.length + " rule groups");

      rules.forEach((ruleGroup, index) => {
        console.log(`Evaluating rule group ${index}:`, ruleGroup);

        const targetField = form.querySelector(
          `[name="${ruleGroup.target_field}"]`
        );
        if (!targetField) {
          console.warn(
            `Target field "${ruleGroup.target_field}" not found in form`
          );
          return;
        }

        // Get the wrapper element (usually the p tag containing the field)
        const targetWrapper = targetField.closest(
          ".wpcf7-form-control-wrap"
        )?.parentElement;
        if (!targetWrapper) {
          console.warn(
            `Wrapper element not found for field "${ruleGroup.target_field}"`
          );
          return;
        }

        // Evaluate all conditions in this group
        const results = ruleGroup.conditions.map((condition, condIndex) => {
          const result = evaluateCondition(form, condition);
          console.log(`  Condition ${condIndex} result:`, result, condition);
          return result;
        });

        console.log(`All condition results:`, results);

        // Determine if the field should be shown or hidden
        let shouldShow;

        if (ruleGroup.logic === "all") {
          // AND logic - all conditions must be true
          shouldShow = results.every((result) => result === true);
        } else if (ruleGroup.logic === "any") {
          // OR logic - at least one condition must be true
          shouldShow = results.some((result) => result === true);
        } else {
          // Default to AND logic if logic is not specified or invalid
          console.warn(
            `Unknown logic type "${ruleGroup.logic}", defaulting to "all"`
          );
          shouldShow = results.every((result) => result === true);
        }

        console.log(
          `Should show field "${ruleGroup.target_field}": ${shouldShow} (action: ${ruleGroup.action})`
        );

        // Apply the action (show/hide)
        if (ruleGroup.action === "show") {
          targetWrapper.style.display = shouldShow ? "" : "none";
          targetWrapper.style.visibility = shouldShow ? "" : "hidden";
        } else {
          targetWrapper.style.display = shouldShow ? "none" : "";
          targetWrapper.style.visibility = shouldShow ? "hidden" : "";
        }

        // Also add a class for CSS styling
        if (shouldShow) {
          targetWrapper.classList.remove("la-addons-hidden");
          targetWrapper.classList.add("la-addons-visible");
        } else {
          targetWrapper.classList.remove("la-addons-visible");
          targetWrapper.classList.add("la-addons-hidden");
        }
      });
    }

    // Evaluate a single condition
    function evaluateCondition(form, condition) {
      if (!condition || !condition.field) {
        console.error("Invalid condition:", condition);
        return false;
      }

      const field = form.querySelector(`[name="${condition.field}"]`);
      if (!field) {
        console.warn(
          `Field "${condition.field}" not found for condition evaluation`
        );
        return false;
      }

      let fieldValue = field.value;

      // Handle special field types
      if (field.type === "checkbox" || field.type === "radio") {
        const checkedFields = form.querySelectorAll(
          `[name="${condition.field}"]:checked`
        );
        if (checkedFields.length > 0) {
          fieldValue = Array.from(checkedFields)
            .map((f) => f.value)
            .join(",");
        } else {
          fieldValue = "";
        }
      }

      console.log(
        `Evaluating condition: field="${condition.field}", value="${fieldValue}", operator="${condition.operator}", expected="${condition.value}"`
      );

      // Ensure we have the required condition properties
      if (!condition.operator) {
        console.error("Missing operator in condition:", condition);
        return false;
      }

      // Compare based on operator
      switch (condition.operator) {
        case "equal":
          return fieldValue === condition.value;

        case "not_equal":
          return fieldValue !== condition.value;

        case "contains":
          return fieldValue.includes(condition.value);

        case "not_contains":
          return !fieldValue.includes(condition.value);

        case "greater_than":
          return parseFloat(fieldValue) > parseFloat(condition.value);

        case "less_than":
          return parseFloat(fieldValue) < parseFloat(condition.value);

        case "starts_with":
          return fieldValue.startsWith(condition.value);

        case "ends_with":
          return fieldValue.endsWith(condition.value);

        case "is_empty":
          return fieldValue === "";

        case "is_not_empty":
          return fieldValue !== "";

        default:
          return false;
      }
    }
  });
}

// Initialize on DOM ready
document.addEventListener("DOMContentLoaded", initializeConditionalLogic);

// Also initialize when CF7 forms are loaded/reloaded
document.addEventListener("wpcf7mailsent", function () {
  // Re-initialize after form submission in case form is reloaded
  setTimeout(initializeConditionalLogic, 100);
});

document.addEventListener("wpcf7invalid", function () {
  // Re-initialize after validation errors
  setTimeout(initializeConditionalLogic, 100);
});

// Initialize when new forms are added to the page (for dynamic content)
if (window.MutationObserver) {
  const observer = new MutationObserver(function (mutations) {
    let shouldReinitialize = false;
    mutations.forEach(function (mutation) {
      if (mutation.type === "childList") {
        mutation.addedNodes.forEach(function (node) {
          if (node.nodeType === 1) {
            // Element node
            if (node.classList && node.classList.contains("wpcf7-form")) {
              shouldReinitialize = true;
            } else if (
              node.querySelector &&
              node.querySelector(".wpcf7-form")
            ) {
              shouldReinitialize = true;
            }
          }
        });
      }
    });

    if (shouldReinitialize) {
      console.log("New CF7 form detected, reinitializing conditional logic");
      setTimeout(initializeConditionalLogic, 100);
    }
  });

  observer.observe(document.body, {
    childList: true,
    subtree: true,
  });
}
