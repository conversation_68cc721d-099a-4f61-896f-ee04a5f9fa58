/* CF7 Editor Tabs */
.cf7-editor-tabs .ui-tabs-nav {
  display: flex;
  margin: 0;
  padding: 0;
  border-bottom: 1px solid #ccc;
}

.cf7-editor-tabs .ui-tabs-nav li {
  margin: 0;
  padding: 0;
  list-style: none;
}

.cf7-editor-tabs .ui-tabs-nav li a {
  display: block;
  padding: 10px 15px;
  text-decoration: none;
  color: #555;
  border: 1px solid transparent;
  border-bottom: none;
}

.cf7-editor-tabs .ui-tabs-nav li.ui-tabs-active a {
  border-color: #ccc;
  border-bottom-color: #f1f1f1;
  background: #f1f1f1;
}

/* Signature Options Panel */
#signature-options-panel {
  padding: 15px;
  background: #fff;
  border: 1px solid #ccc;
  border-top: none;
}

/* Frontend Signature Pad Styling */
.la-addons-signature-pad {
  margin-bottom: 20px;
}

.la-addons-signature-pad canvas {
  cursor: crosshair;
  border: 1px solid #ccc !important;
  display: block !important;
  box-sizing: border-box !important;
  margin-bottom: 5px !important;
  /* Remove any max-width constraints */
  max-width: none !important;
}

.signature-clear-button {
  background: #f7f7f7;
  border: 1px solid #ccc;
  padding: 5px 10px;
  cursor: pointer;
  font-size: 14px;
}

.signature-clear-button:hover {
  background: #f0f0f0;
}

/* Submission details styling */
.submission-details td {
  padding: 0;
}

.submission-details table {
  margin: 10px;
  border: 1px solid #e5e5e5;
}

.submission-details table th {
  background-color: #f9f9f9;
  font-weight: 600;
}

.submission-details table td,
.submission-details table th {
  padding: 8px 10px;
}

/* Improve filter form layout */
.wrap form {
  margin-bottom: 15px;
  display: flex;
  align-items: center;
  gap: 10px;
  flex-wrap: wrap;
}

.wrap form input[type="search"],
.wrap form select,
.wrap form input[type="date"] {
  min-width: 180px;
}

/* Make the table more readable */
.wp-list-table th {
  font-weight: 600;
}

.wp-list-table .toggle-details {
  margin: 0;
}

/* Conditional Logic UI Styling */
.la-addons-panel {
  background: #fff;
  border: 1px solid #e5e5e5;
  box-shadow: 0 1px 1px rgba(0, 0, 0, 0.04);
  padding: 15px;
  margin-top: 15px;
}

.rule-group {
  background: #f9f9f9;
  border: 1px solid #e5e5e5;
  margin-bottom: 20px;
  padding: 15px;
  border-radius: 3px;
}

.rule-group-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
  border-bottom: 1px solid #e5e5e5;
  padding-bottom: 10px;
}

.rule-group-header h3 {
  margin: 0;
  font-size: 14px;
  font-weight: 600;
}

.target-field-container {
  display: flex;
  flex-wrap: wrap;
  align-items: center;
  gap: 10px;
  margin-bottom: 15px;
  padding: 10px;
  background: #fff;
  border-radius: 3px;
  border: 1px solid #e5e5e5;
}

.conditions-container {
  margin-bottom: 15px;
}

.condition-row {
  display: flex;
  flex-wrap: wrap;
  align-items: center;
  gap: 10px;
  margin-bottom: 10px;
  padding: 10px;
  background: #fff;
  border-radius: 3px;
  border: 1px solid #e5e5e5;
}

.condition-field-select,
.operator-select {
  min-width: 150px;
}

.condition-value-input {
  flex: 1;
  min-width: 150px;
}

.rule-actions {
  margin-top: 20px;
}

.no-rules-message {
  background: #f7f7f7;
  border-left: 4px solid #0073aa;
  padding: 10px 15px;
  margin-bottom: 20px;
}

/* Responsive adjustments */
@media screen and (max-width: 782px) {
  .target-field-container,
  .condition-row {
    flex-direction: column;
    align-items: flex-start;
  }

  .condition-field-select,
  .operator-select,
  .condition-value-input {
    width: 100%;
  }
}

/* Tooltips for better UX */
.la-addons-tooltip {
  position: relative;
  display: inline-block;
  margin-left: 5px;
  cursor: help;
}

.la-addons-tooltip .dashicons {
  color: #0073aa;
  font-size: 16px;
  width: 16px;
  height: 16px;
}

.la-addons-tooltip .tooltip-text {
  visibility: hidden;
  width: 200px;
  background-color: #333;
  color: #fff;
  text-align: center;
  border-radius: 6px;
  padding: 5px;
  position: absolute;
  z-index: 1;
  bottom: 125%;
  left: 50%;
  margin-left: -100px;
  opacity: 0;
  transition: opacity 0.3s;
}

.la-addons-tooltip:hover .tooltip-text {
  visibility: visible;
  opacity: 1;
}
